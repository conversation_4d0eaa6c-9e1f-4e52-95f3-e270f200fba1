# For more information about build system see
# https://docs.espressif.com/projects/esp-idf/en/latest/api-guides/build-system.html
# The following five lines of boilerplate have to be in your project's
# CMakeLists in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)
include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(09_LVGL_V8_Test)

# Create mmap assets partition for GIF files
spiffs_create_partition_assets(
    assets                          # Partition name
    assets                          # Assets folder
    FLASH_IN_PROJECT               # Flash with project
    MMAP_FILE_SUPPORT_FORMAT ".gif" # Support GIF files
)
