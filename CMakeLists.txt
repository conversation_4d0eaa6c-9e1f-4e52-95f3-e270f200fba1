# For more information about build system see
# https://docs.espressif.com/projects/esp-idf/en/latest/api-guides/build-system.html
# The following five lines of boilerplate have to be in your project's
# CMakeLists in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)
include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(09_LVGL_V8_Test)

# Create mmap assets partition for GIF files
spiffs_create_partition_assets(
    assets                          # Partition name
    assets                          # Assets folder
    FLASH_IN_PROJECT               # Flash with project
    MMAP_FILE_SUPPORT_FORMAT ".gif" # Support GIF files
)
set(options
    FLASH_IN_PROJECT,
    FLASH_APPEND_APP,
    MMAP_SUPPORT_SJPG,
    MMAP_SUPPORT_SPNG,
    MMAP_SUPPORT_QOI,
    MMAP_SUPPORT_SQOI,
    MMAP_SUPPORT_RAW,
    MMAP_RAW_DITHER,
    MMAP_RAW_BGR_MODE)

set(one_value_args
    MMAP_FILE_SUPPORT_FORMAT,
    MMAP_SPLIT_HEIGHT,
    MMAP_RAW_FILE_FORMAT)