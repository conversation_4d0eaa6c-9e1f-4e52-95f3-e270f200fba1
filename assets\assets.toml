# ESP-IDF Assets Configuration for GIF Player
# This file configures how assets are packaged using esp_mmap_assets

[assets]
# Asset namespace - used to generate function names
namespace = "gif_assets"

# Asset files to include
[[assets.files]]
name = "test_gif"
path = "test.gif"
description = "Test animated GIF for gif_player component"

[[assets.files]]
name = "nuko_joy"
path = "nukoJoy.gif"
description = "Nuko Joy animated GIF"

[[assets.files]]
name = "output_100x100"
path = "output_100x100.gif"
description = "100x100 output GIF"

# Generation settings
[generation]
# Generate header file with asset declarations
generate_header = true
header_file = "gif_assets.h"

# Generate source file with asset data
generate_source = true
source_file = "gif_assets.c"

# Memory alignment for assets (must be power of 2)
alignment = 4

# Compression settings (optional)
[compression]
# Enable compression for assets
enabled = false
# Compression algorithm: "gzip" or "lz4"
algorithm = "gzip"
