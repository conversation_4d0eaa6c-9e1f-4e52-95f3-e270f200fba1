# ESP-IDF LVGL GIF Player Component

这是一个用于ESP-IDF项目的可复用LVGL GIF播放器组件，支持嵌入式GIF数据播放和缩放功能。

## 特性

- ✅ 支持嵌入式GIF数据（无需文件系统）
- ✅ 自动循环播放
- ✅ 可配置缩放比例
- ✅ 简单易用的API
- ✅ 完整的错误处理
- ✅ 内存管理优化

## 快速开始

### 1. 启用LVGL GIF支持

在 `sdkconfig` 中确保启用：
```
CONFIG_LV_USE_GIF=y
```

### 2. 转换GIF文件为C数组

使用提供的Python脚本：
```bash
python tools/gif_to_c_array.py your_gif.gif components/gif_player/your_gif_data.h your_gif_data
```

### 3. 更新CMakeLists.txt

在组件的CMakeLists.txt中添加生成的源文件：
```cmake
idf_component_register(SRCS "gif_player.c" "your_gif_data.c"
                    INCLUDE_DIRS "."
                    REQUIRES lvgl)
```

### 4. 在代码中使用

```c
#include "gif_player.h"
#include "your_gif_data.h"

// 初始化GIF播放器
gif_player_init();

// 配置GIF播放器
gif_player_config_t config = {
    .gif_data = your_gif_data,
    .gif_data_size = your_gif_data_size,
    .parent = lv_scr_act(),
    .x = 100,
    .y = 100,
    .zoom = 512,        // 200% 缩放
    .auto_start = true,
    .loop_forever = true
};

// 创建GIF播放器
gif_player_handle_t handle;
gif_player_create(&config, &handle);
```

## 缩放说明

缩放使用LVGL的zoom系统：
- `256` = 100% (原始大小)
- `512` = 200% (2倍大小)
- `128` = 50% (一半大小)
- `1024` = 400% (4倍大小)

### 动态缩放

```c
// 运行时改变缩放
gif_player_set_zoom(handle, 768);  // 300% 缩放
```

## API参考

### 初始化函数
- `gif_player_init()` - 初始化组件

### 播放控制
- `gif_player_create()` - 创建GIF播放器实例
- `gif_player_start()` - 开始播放
- `gif_player_stop()` - 停止播放
- `gif_player_restart()` - 重新开始播放
- `gif_player_destroy()` - 销毁实例

### 属性设置
- `gif_player_set_pos()` - 设置位置
- `gif_player_set_zoom()` - 设置缩放比例
- `gif_player_get_obj()` - 获取LVGL对象

## 工具使用

### GIF转换工具

`tools/gif_to_c_array.py` 用法：
```bash
python gif_to_c_array.py <gif_file> <output_header> <array_name>
```

示例：
```bash
python tools/gif_to_c_array.py animation.gif my_animation.h my_animation_data
```

这将生成：
- `my_animation.h` - 头文件
- `my_animation.c` - 源文件

## 最佳实践

1. **内存优化**：GIF数据存储在Flash中，不占用RAM
2. **性能考虑**：大尺寸GIF可能影响性能，建议适当缩放
3. **位置计算**：考虑缩放后的实际尺寸来计算居中位置
4. **错误处理**：始终检查函数返回值

## 示例项目结构

```
project/
├── components/
│   └── gif_player/
│       ├── CMakeLists.txt
│       ├── gif_player.h
│       ├── gif_player.c
│       ├── your_gif_data.h
│       ├── your_gif_data.c
│       └── README.md
├── tools/
│   └── gif_to_c_array.py
└── main/
    └── main.c
```

## 故障排除

1. **编译错误**：确保在sdkconfig中启用了`CONFIG_LV_USE_GIF=y`
2. **显示问题**：检查GIF数据是否正确生成
3. **内存不足**：考虑减小GIF文件大小或降低缩放比例

## 许可证

本组件遵循ESP-IDF的许可证条款。
