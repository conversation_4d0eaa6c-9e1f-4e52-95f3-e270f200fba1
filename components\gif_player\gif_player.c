/**
 * @file gif_player.c
 * @brief ESP-IDF component for playing animated GIFs using LVGL
 */

#include "gif_player.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "esp_mmap_assets.h"
#include "mmap_generate_assets.h"
#include <string.h>
#include <stdio.h>

static const char *TAG = "gif_player";

// Global mmap assets handle
static mmap_assets_handle_t g_assets_handle = NULL;

/**
 * @brief Get asset index by name
 */
static int get_asset_index_by_name(const char *asset_name)
{
    if (!asset_name) {
        return -1;
    }

    if (strcmp(asset_name, "test_gif") == 0) {
        return MMAP_ASSETS_TEST_GIF;
    } else if (strcmp(asset_name, "nuko_joy") == 0) {
        return MMAP_ASSETS_NUKOJOY_GIF;
    } else if (strcmp(asset_name, "output_100x100") == 0) {
        return MMAP_ASSETS_OUTPUT_100X100_GIF;
    }

    return -1;
}

/**
 * @brief Internal GIF player structure
 */
typedef struct gif_player_s {
    lv_obj_t *gif_obj;              /*!< LVGL GIF object */
    lv_img_dsc_t img_dsc;           /*!< LVGL image descriptor */
    bool is_playing;                /*!< Playback state */
    bool loop_forever;              /*!< Loop forever flag */
    bool is_mmap_asset;             /*!< True if using mmap asset, false if embedded data */
} gif_player_t;

/**
 * @brief Event callback for GIF ready event (end of animation)
 */
static void gif_ready_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *gif_obj = lv_event_get_target(e);
    
    if (code == LV_EVENT_READY) {
        // Get the gif_player handle from user data
        gif_player_t *player = (gif_player_t *)lv_event_get_user_data(e);
        
        if (player && player->loop_forever) {
            ESP_LOGD(TAG, "GIF animation finished, restarting...");
            lv_gif_restart(gif_obj);
        }
    }
}

gif_player_err_t gif_player_init(void)
{
    // Initialize mmap assets if not already done
    if (g_assets_handle == NULL) {
        const mmap_assets_config_t config = {
            .partition_label = "assets",
            .max_files = MMAP_ASSETS_FILES,
            .checksum = MMAP_ASSETS_CHECKSUM,
            .flags = {
                .mmap_enable = true,
                .app_bin_check = true,
                .full_check = false,
                .metadata_check = false,
            }
        };

        esp_err_t ret = mmap_assets_new(&config, &g_assets_handle);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to initialize mmap assets: %s", esp_err_to_name(ret));
            return GIF_PLAYER_ERR_MEMORY;
        }

        ESP_LOGI(TAG, "Mmap assets initialized with %d files", MMAP_ASSETS_FILES);
    }

    ESP_LOGI(TAG, "GIF player initialized");
    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_create(const gif_player_config_t *config, gif_player_handle_t *handle)
{
    if (!config || !handle || !config->gif_data || !config->parent) {
        ESP_LOGE(TAG, "Invalid arguments");
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    // Check if we have either embedded data or asset name
    if (!config->gif_data && !config->asset_name) {
        ESP_LOGE(TAG, "Either gif_data or asset_name must be provided");
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    // Allocate memory for the player structure
    gif_player_t *player = (gif_player_t *)heap_caps_malloc(sizeof(gif_player_t), MALLOC_CAP_8BIT);
    if (!player) {
        ESP_LOGE(TAG, "Failed to allocate memory for GIF player");
        return GIF_PLAYER_ERR_MEMORY;
    }

    // Initialize the structure
    memset(player, 0, sizeof(gif_player_t));
    player->loop_forever = config->loop_forever;

    const uint8_t *gif_data = NULL;
    size_t gif_data_size = 0;

    // Load GIF data from either embedded array or mmap asset
    if (config->asset_name) {
        // Load from mmap asset
        if (!g_assets_handle) {
            ESP_LOGE(TAG, "Mmap assets not initialized. Call gif_player_init() first.");
            free(player);
            return GIF_PLAYER_ERR_MEMORY;
        }

        int asset_index = get_asset_index_by_name(config->asset_name);
        if (asset_index < 0) {
            ESP_LOGE(TAG, "Asset '%s' not found", config->asset_name);
            free(player);
            return GIF_PLAYER_ERR_FILE_NOT_FOUND;
        }

        gif_data = mmap_assets_get_mem(g_assets_handle, asset_index);
        gif_data_size = mmap_assets_get_size(g_assets_handle, asset_index);

        if (!gif_data || gif_data_size <= 0) {
            ESP_LOGE(TAG, "Failed to get asset data for '%s'", config->asset_name);
            free(player);
            return GIF_PLAYER_ERR_FILE_NOT_FOUND;
        }

        player->is_mmap_asset = true;
        ESP_LOGI(TAG, "Loaded GIF asset '%s' (%d bytes) from mmap", config->asset_name, gif_data_size);
    } else {
        // Use embedded data
        gif_data = config->gif_data;
        gif_data_size = config->gif_data_size;
        player->is_mmap_asset = false;

        ESP_LOGI(TAG, "Using embedded GIF data (%d bytes)", gif_data_size);
    }

    // Setup image descriptor for GIF data
    player->img_dsc.header.always_zero = 0;
    player->img_dsc.header.w = 0;  // Will be set by LVGL
    player->img_dsc.header.h = 0;  // Will be set by LVGL
    player->img_dsc.header.cf = LV_IMG_CF_RAW;
    player->img_dsc.data_size = gif_data_size;
    player->img_dsc.data = gif_data;

    // Create the LVGL GIF object
    player->gif_obj = lv_gif_create(config->parent);
    if (!player->gif_obj) {
        ESP_LOGE(TAG, "Failed to create LVGL GIF object");
        free(player);
        return GIF_PLAYER_ERR_MEMORY;
    }

    // Set position
    lv_obj_set_pos(player->gif_obj, config->x, config->y);

    // Set zoom if specified
    if (config->zoom > 0) {
        lv_img_set_zoom(player->gif_obj, config->zoom);
    }

    // Add event callback for GIF ready event
    lv_obj_add_event_cb(player->gif_obj, gif_ready_event_cb, LV_EVENT_READY, player);

    // Set the GIF source using the image descriptor
    lv_gif_set_src(player->gif_obj, &player->img_dsc);

    ESP_LOGI(TAG, "GIF player created successfully (%d bytes)", gif_data_size);

    player->is_playing = config->auto_start;

    *handle = player;
    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_create_from_asset(const char *asset_name, lv_obj_t *parent,
                                               lv_coord_t x, lv_coord_t y, gif_player_handle_t *handle)
{
    if (!asset_name || !parent || !handle) {
        ESP_LOGE(TAG, "Invalid arguments");
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_config_t config = {
        .gif_data = NULL,
        .gif_data_size = 0,
        .asset_name = asset_name,
        .parent = parent,
        .x = x,
        .y = y,
        .zoom = 256,  // 100% zoom
        .auto_start = true,
        .loop_forever = true
    };

    return gif_player_create(&config, handle);
}

gif_player_err_t gif_player_start(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    
    if (!player->is_playing) {
        lv_gif_restart(player->gif_obj);
        player->is_playing = true;
        ESP_LOGD(TAG, "GIF playback started");
    }

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_stop(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    player->is_playing = false;
    
    // Note: LVGL GIF doesn't have a direct stop function, 
    // but we can track the state for our own logic
    ESP_LOGD(TAG, "GIF playback stopped");

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_restart(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_gif_restart(player->gif_obj);
    player->is_playing = true;
    
    ESP_LOGD(TAG, "GIF playback restarted");

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_set_pos(gif_player_handle_t handle, lv_coord_t x, lv_coord_t y)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_obj_set_pos(player->gif_obj, x, y);

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_set_zoom(gif_player_handle_t handle, uint16_t zoom)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_img_set_zoom(player->gif_obj, zoom);

    ESP_LOGD(TAG, "GIF zoom set to %d (%.1f%%)", zoom, (float)zoom / 256.0 * 100.0);

    return GIF_PLAYER_OK;
}

lv_obj_t *gif_player_get_obj(gif_player_handle_t handle)
{
    if (!handle) {
        return NULL;
    }

    gif_player_t *player = (gif_player_t *)handle;
    return player->gif_obj;
}

gif_player_err_t gif_player_destroy(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;

    // Clean up LVGL object
    if (player->gif_obj) {
        lv_obj_del(player->gif_obj);
    }

    // Note: We don't release the global mmap assets handle here
    // as it may be used by other gif_player instances

    free(player);

    ESP_LOGD(TAG, "GIF player destroyed");

    return GIF_PLAYER_OK;
}
