/**
 * @file gif_player.c
 * @brief ESP-IDF component for playing animated GIFs using LVGL
 */

#include "gif_player.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "esp_mmap_assets.h"
#include <string.h>
#include <stdio.h>

static const char *TAG = "gif_player";

/**
 * @brief Internal GIF player structure
 */
typedef struct gif_player_s {
    lv_obj_t *gif_obj;              /*!< LVGL GIF object */
    lv_img_dsc_t img_dsc;           /*!< LVGL image descriptor */
    bool is_playing;                /*!< Playback state */
    bool loop_forever;              /*!< Loop forever flag */
    mmap_assets_handle_t mmap_handle; /*!< Memory map handle for assets */
    bool is_mmap_asset;             /*!< True if using mmap asset, false if embedded data */
} gif_player_t;

/**
 * @brief Event callback for GIF ready event (end of animation)
 */
static void gif_ready_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *gif_obj = lv_event_get_target(e);
    
    if (code == LV_EVENT_READY) {
        // Get the gif_player handle from user data
        gif_player_t *player = (gif_player_t *)lv_event_get_user_data(e);
        
        if (player && player->loop_forever) {
            ESP_LOGD(TAG, "GIF animation finished, restarting...");
            lv_gif_restart(gif_obj);
        }
    }
}

gif_player_err_t gif_player_init(void)
{
    ESP_LOGI(TAG, "GIF player initialized");
    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_create(const gif_player_config_t *config, gif_player_handle_t *handle)
{
    if (!config || !handle || !config->gif_data || !config->parent) {
        ESP_LOGE(TAG, "Invalid arguments");
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    // Check if we have either embedded data or asset name
    if (!config->gif_data && !config->asset_name) {
        ESP_LOGE(TAG, "Either gif_data or asset_name must be provided");
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    // Allocate memory for the player structure
    gif_player_t *player = (gif_player_t *)heap_caps_malloc(sizeof(gif_player_t), MALLOC_CAP_8BIT);
    if (!player) {
        ESP_LOGE(TAG, "Failed to allocate memory for GIF player");
        return GIF_PLAYER_ERR_MEMORY;
    }

    // Initialize the structure
    memset(player, 0, sizeof(gif_player_t));
    player->loop_forever = config->loop_forever;

    const uint8_t *gif_data = NULL;
    size_t gif_data_size = 0;

    // Load GIF data from either embedded array or mmap asset
    if (config->asset_name) {
        // Note: For now, we'll use a placeholder for mmap asset loading
        // The actual implementation will depend on the generated header file
        ESP_LOGE(TAG, "Mmap asset loading not yet implemented for '%s'", config->asset_name);
        free(player);
        return GIF_PLAYER_ERR_FILE_NOT_FOUND;
    } else {
        // Use embedded data
        gif_data = config->gif_data;
        gif_data_size = config->gif_data_size;
        player->is_mmap_asset = false;

        ESP_LOGI(TAG, "Using embedded GIF data (%d bytes)", gif_data_size);
    }

    // Setup image descriptor for GIF data
    player->img_dsc.header.always_zero = 0;
    player->img_dsc.header.w = 0;  // Will be set by LVGL
    player->img_dsc.header.h = 0;  // Will be set by LVGL
    player->img_dsc.header.cf = LV_IMG_CF_RAW;
    player->img_dsc.data_size = gif_data_size;
    player->img_dsc.data = gif_data;

    // Create the LVGL GIF object
    player->gif_obj = lv_gif_create(config->parent);
    if (!player->gif_obj) {
        ESP_LOGE(TAG, "Failed to create LVGL GIF object");
        if (player->is_mmap_asset && player->mmap_handle) {
            mmap_assets_release(player->mmap_handle);
        }
        free(player);
        return GIF_PLAYER_ERR_MEMORY;
    }

    // Set position
    lv_obj_set_pos(player->gif_obj, config->x, config->y);

    // Set zoom if specified
    if (config->zoom > 0) {
        lv_img_set_zoom(player->gif_obj, config->zoom);
    }

    // Add event callback for GIF ready event
    lv_obj_add_event_cb(player->gif_obj, gif_ready_event_cb, LV_EVENT_READY, player);

    // Set the GIF source using the image descriptor
    lv_gif_set_src(player->gif_obj, &player->img_dsc);

    ESP_LOGI(TAG, "GIF player created successfully (%d bytes)", gif_data_size);

    player->is_playing = config->auto_start;

    *handle = player;
    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_create_from_asset(const char *asset_name, lv_obj_t *parent,
                                               lv_coord_t x, lv_coord_t y, gif_player_handle_t *handle)
{
    if (!asset_name || !parent || !handle) {
        ESP_LOGE(TAG, "Invalid arguments");
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_config_t config = {
        .gif_data = NULL,
        .gif_data_size = 0,
        .asset_name = asset_name,
        .parent = parent,
        .x = x,
        .y = y,
        .zoom = 256,  // 100% zoom
        .auto_start = true,
        .loop_forever = true
    };

    return gif_player_create(&config, handle);
}

gif_player_err_t gif_player_start(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    
    if (!player->is_playing) {
        lv_gif_restart(player->gif_obj);
        player->is_playing = true;
        ESP_LOGD(TAG, "GIF playback started");
    }

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_stop(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    player->is_playing = false;
    
    // Note: LVGL GIF doesn't have a direct stop function, 
    // but we can track the state for our own logic
    ESP_LOGD(TAG, "GIF playback stopped");

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_restart(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_gif_restart(player->gif_obj);
    player->is_playing = true;
    
    ESP_LOGD(TAG, "GIF playback restarted");

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_set_pos(gif_player_handle_t handle, lv_coord_t x, lv_coord_t y)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_obj_set_pos(player->gif_obj, x, y);

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_set_zoom(gif_player_handle_t handle, uint16_t zoom)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_img_set_zoom(player->gif_obj, zoom);

    ESP_LOGD(TAG, "GIF zoom set to %d (%.1f%%)", zoom, (float)zoom / 256.0 * 100.0);

    return GIF_PLAYER_OK;
}

lv_obj_t *gif_player_get_obj(gif_player_handle_t handle)
{
    if (!handle) {
        return NULL;
    }

    gif_player_t *player = (gif_player_t *)handle;
    return player->gif_obj;
}

gif_player_err_t gif_player_destroy(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;

    // Clean up LVGL object
    if (player->gif_obj) {
        lv_obj_del(player->gif_obj);
    }

    // Release mmap asset if used
    if (player->is_mmap_asset && player->mmap_handle) {
        mmap_assets_release(player->mmap_handle);
        ESP_LOGD(TAG, "Released mmap asset");
    }

    free(player);

    ESP_LOGD(TAG, "GIF player destroyed");

    return GIF_PLAYER_OK;
}
