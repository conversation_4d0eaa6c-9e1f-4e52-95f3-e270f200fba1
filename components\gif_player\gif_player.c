/**
 * @file gif_player.c
 * @brief ESP-IDF component for playing animated GIFs using LVGL
 */

#include "gif_player.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "esp_mmap_assets.h"
#include <string.h>
#include <stdio.h>

static const char *TAG = "gif_player";

// Global mmap assets handle
static mmap_assets_handle_t g_mmap_handle = NULL;

/**
 * @brief Internal GIF player structure
 */
typedef struct gif_player_s {
    lv_obj_t *gif_obj;              /*!< LVGL GIF object */
    lv_img_dsc_t img_dsc;           /*!< LVGL image descriptor */
    bool is_playing;                /*!< Playback state */
    bool loop_forever;              /*!< Loop forever flag */
    gif_player_src_type_t src_type; /*!< Data source type */
    mmap_assets_handle_t mmap_handle; /*!< Memory-mapped assets handle */
} gif_player_t;

/**
 * @brief Event callback for GIF ready event (end of animation)
 */
static void gif_ready_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *gif_obj = lv_event_get_target(e);
    
    if (code == LV_EVENT_READY) {
        // Get the gif_player handle from user data
        gif_player_t *player = (gif_player_t *)lv_event_get_user_data(e);
        
        if (player && player->loop_forever) {
            ESP_LOGD(TAG, "GIF animation finished, restarting...");
            lv_gif_restart(gif_obj);
        }
    }
}

gif_player_err_t gif_player_init(void)
{
    // Initialize memory-mapped assets if not already done
    if (g_mmap_handle == NULL) {
        // Include the generated header file for asset definitions
        #include "mmap_generate_assets.h"

        const mmap_assets_config_t config = {
            .partition_label = "gif_assets",
            .max_files = MMAP_ASSETS_FILES,
            .checksum = MMAP_ASSETS_CHECKSUM,
            .flags = {
                .mmap_enable = true,
                .app_bin_check = true,
            },
        };

        esp_err_t ret = mmap_assets_new(&config, &g_mmap_handle);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to initialize mmap assets: %s", esp_err_to_name(ret));
            return GIF_PLAYER_ERR_MEMORY;
        }

        ESP_LOGI(TAG, "Memory-mapped assets initialized successfully");
    }

    ESP_LOGI(TAG, "GIF player initialized");
    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_create(const gif_player_config_t *config, gif_player_handle_t *handle)
{
    if (!config || !handle || !config->parent) {
        ESP_LOGE(TAG, "Invalid arguments");
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    // Validate source-specific parameters
    if (config->src_type == GIF_PLAYER_SRC_EMBEDDED) {
        if (!config->src.embedded.gif_data || config->src.embedded.gif_data_size == 0) {
            ESP_LOGE(TAG, "Invalid embedded data parameters");
            return GIF_PLAYER_ERR_INVALID_ARG;
        }
    } else if (config->src_type == GIF_PLAYER_SRC_MMAP) {
        if (g_mmap_handle == NULL) {
            ESP_LOGE(TAG, "Memory-mapped assets not initialized. Call gif_player_init() first");
            return GIF_PLAYER_ERR_INVALID_ARG;
        }
    }

    // Allocate memory for the player structure
    gif_player_t *player = (gif_player_t *)heap_caps_malloc(sizeof(gif_player_t), MALLOC_CAP_8BIT);
    if (!player) {
        ESP_LOGE(TAG, "Failed to allocate memory for GIF player");
        return GIF_PLAYER_ERR_MEMORY;
    }

    // Initialize the structure
    memset(player, 0, sizeof(gif_player_t));
    player->loop_forever = config->loop_forever;
    player->src_type = config->src_type;
    player->mmap_handle = g_mmap_handle;

    // Setup image descriptor based on source type
    player->img_dsc.header.always_zero = 0;
    player->img_dsc.header.w = 0;  // Will be set by LVGL
    player->img_dsc.header.h = 0;  // Will be set by LVGL
    player->img_dsc.header.cf = LV_IMG_CF_RAW;

    if (config->src_type == GIF_PLAYER_SRC_EMBEDDED) {
        player->img_dsc.data_size = config->src.embedded.gif_data_size;
        player->img_dsc.data = config->src.embedded.gif_data;
    } else if (config->src_type == GIF_PLAYER_SRC_MMAP) {
        const uint8_t *gif_data = mmap_assets_get_mem(g_mmap_handle, config->src.mmap.asset_index);
        int gif_size = mmap_assets_get_size(g_mmap_handle, config->src.mmap.asset_index);

        if (!gif_data || gif_size <= 0) {
            ESP_LOGE(TAG, "Failed to get GIF data from mmap assets");
            free(player);
            return GIF_PLAYER_ERR_INVALID_ARG;
        }

        player->img_dsc.data_size = gif_size;
        player->img_dsc.data = gif_data;
    }

    // Create the LVGL GIF object
    player->gif_obj = lv_gif_create(config->parent);
    if (!player->gif_obj) {
        ESP_LOGE(TAG, "Failed to create LVGL GIF object");
        free(player);
        return GIF_PLAYER_ERR_MEMORY;
    }

    // Set position
    lv_obj_set_pos(player->gif_obj, config->x, config->y);

    // Set zoom if specified
    if (config->zoom > 0) {
        lv_img_set_zoom(player->gif_obj, config->zoom);
    }

    // Add event callback for GIF ready event
    lv_obj_add_event_cb(player->gif_obj, gif_ready_event_cb, LV_EVENT_READY, player);

    // Set the GIF source using the image descriptor
    lv_gif_set_src(player->gif_obj, &player->img_dsc);

    if (config->src_type == GIF_PLAYER_SRC_EMBEDDED) {
        ESP_LOGI(TAG, "GIF player created with embedded data (%d bytes)", config->src.embedded.gif_data_size);
    } else {
        const char *asset_name = mmap_assets_get_name(g_mmap_handle, config->src.mmap.asset_index);
        ESP_LOGI(TAG, "GIF player created with mmap asset '%s' (%d bytes)",
                 asset_name ? asset_name : "unknown", player->img_dsc.data_size);
    }

    player->is_playing = config->auto_start;

    *handle = player;
    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_create_from_mmap(int asset_index, lv_obj_t *parent,
                                              lv_coord_t x, lv_coord_t y,
                                              gif_player_handle_t *handle)
{
    gif_player_config_t config = {
        .src_type = GIF_PLAYER_SRC_MMAP,
        .src.mmap.asset_index = asset_index,
        .parent = parent,
        .x = x,
        .y = y,
        .zoom = 0,  // No zoom by default
        .auto_start = true,
        .loop_forever = true
    };

    return gif_player_create(&config, handle);
}

gif_player_err_t gif_player_start(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    
    if (!player->is_playing) {
        lv_gif_restart(player->gif_obj);
        player->is_playing = true;
        ESP_LOGD(TAG, "GIF playback started");
    }

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_stop(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    player->is_playing = false;
    
    // Note: LVGL GIF doesn't have a direct stop function, 
    // but we can track the state for our own logic
    ESP_LOGD(TAG, "GIF playback stopped");

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_restart(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_gif_restart(player->gif_obj);
    player->is_playing = true;
    
    ESP_LOGD(TAG, "GIF playback restarted");

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_set_pos(gif_player_handle_t handle, lv_coord_t x, lv_coord_t y)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_obj_set_pos(player->gif_obj, x, y);

    return GIF_PLAYER_OK;
}

gif_player_err_t gif_player_set_zoom(gif_player_handle_t handle, uint16_t zoom)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;
    lv_img_set_zoom(player->gif_obj, zoom);

    ESP_LOGD(TAG, "GIF zoom set to %d (%.1f%%)", zoom, (float)zoom / 256.0 * 100.0);

    return GIF_PLAYER_OK;
}

lv_obj_t *gif_player_get_obj(gif_player_handle_t handle)
{
    if (!handle) {
        return NULL;
    }

    gif_player_t *player = (gif_player_t *)handle;
    return player->gif_obj;
}

gif_player_err_t gif_player_destroy(gif_player_handle_t handle)
{
    if (!handle) {
        return GIF_PLAYER_ERR_INVALID_ARG;
    }

    gif_player_t *player = (gif_player_t *)handle;

    // Clean up LVGL object
    if (player->gif_obj) {
        lv_obj_del(player->gif_obj);
    }

    free(player);

    ESP_LOGD(TAG, "GIF player destroyed");

    return GIF_PLAYER_OK;
}
