#!/usr/bin/env python3
"""
Convert GIF file to C array for embedding in ESP-IDF project
"""

import os
import sys

def gif_to_c_array(gif_path, output_path, array_name):
    """Convert GIF file to C array"""
    
    if not os.path.exists(gif_path):
        print(f"Error: GIF file '{gif_path}' not found")
        return False
    
    # Read GIF file
    with open(gif_path, 'rb') as f:
        gif_data = f.read()
    
    # Generate C header file
    header_content = f"""/**
 * @file {os.path.basename(output_path)}
 * @brief Generated GIF data array from {os.path.basename(gif_path)}
 * 
 * This file was automatically generated by gif_to_c_array.py
 * Do not edit manually.
 */

#ifndef {array_name.upper()}_H
#define {array_name.upper()}_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {{
#endif

// GIF data size: {len(gif_data)} bytes
extern const uint8_t {array_name}[];
extern const size_t {array_name}_size;

#ifdef __cplusplus
}}
#endif

#endif /* {array_name.upper()}_H */
"""

    # Generate C source file
    source_content = f"""/**
 * @file {os.path.basename(output_path).replace('.h', '.c')}
 * @brief Generated GIF data array from {os.path.basename(gif_path)}
 * 
 * This file was automatically generated by gif_to_c_array.py
 * Do not edit manually.
 */

#include "{os.path.basename(output_path)}"

// GIF data array ({len(gif_data)} bytes)
const uint8_t {array_name}[] = {{
"""

    # Add data bytes (16 bytes per line)
    for i in range(0, len(gif_data), 16):
        line = "    "
        for j in range(16):
            if i + j < len(gif_data):
                line += f"0x{gif_data[i + j]:02x}"
                if i + j < len(gif_data) - 1:
                    line += ", "
        source_content += line + "\n"
    
    source_content += f"""
}};

const size_t {array_name}_size = sizeof({array_name});
"""

    # Write header file
    with open(output_path, 'w') as f:
        f.write(header_content)
    
    # Write source file
    source_path = output_path.replace('.h', '.c')
    with open(source_path, 'w') as f:
        f.write(source_content)
    
    print(f"Generated {output_path} and {source_path}")
    print(f"Array name: {array_name}")
    print(f"Data size: {len(gif_data)} bytes")
    
    return True

def main():
    if len(sys.argv) != 4:
        print("Usage: python gif_to_c_array.py <gif_file> <output_header> <array_name>")
        print("Example: python gif_to_c_array.py test.gif test_gif.h test_gif_data")
        sys.exit(1)
    
    gif_path = sys.argv[1]
    output_path = sys.argv[2]
    array_name = sys.argv[3]
    
    if gif_to_c_array(gif_path, output_path, array_name):
        print("Conversion completed successfully!")
    else:
        print("Conversion failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
